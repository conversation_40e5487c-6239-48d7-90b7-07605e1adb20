ESX = exports["es_extended"]:getSharedObject()

-- 全局变量
local activeDrugs = {}      -- 活跃的迷药效果
local activeRescues = {}    -- 活跃的求救
local surgeryInProgress = {} -- 进行中的手术

-- 玩家连接时初始化
AddEventHandler('esx:playerLoaded', function(playerId, xPlayer)
    local identifier = xPlayer.identifier
    
    -- 初始化玩家器官状态
    CreateThread(function()
        Wait(2000) -- 等待数据库准备就绪
        GetPlayerOrgans(identifier)
    end)
end)

-- 玩家断开连接时清理
AddEventHandler('esx:playerDropped', function(playerId, reason)
    local identifier = GetPlayerIdentifier(playerId, 0)
    
    -- 清理相关数据
    if activeDrugs[identifier] then
        activeDrugs[identifier] = nil
    end
    
    if surgeryInProgress[identifier] then
        surgeryInProgress[identifier] = nil
    end
    
    -- 清理求救记录
    for callId, rescue in pairs(activeRescues) do
        if rescue.victim == identifier then
            activeRescues[callId] = nil
            break
        end
    end
end)

-- 获取玩家标识符
function GetPlayerIdentifierByServerId(serverId)
    return GetPlayerIdentifier(serverId, 0)
end

-- 检查玩家权限
function HasPermission(playerId, permissionType)
    local xPlayer = ESX.GetPlayerFromId(playerId)
    if not xPlayer then return false end
    
    local permissions = Config.Permissions[permissionType]
    if not permissions then return false end
    
    for _, permission in ipairs(permissions) do
        if xPlayer.job.name == permission then
            return true
        end
        
        -- 检查帮派权限（如果有帮派系统）
        if permission == 'gang' and xPlayer.gang then
            return true
        end
    end
    
    return false
end

-- 发送通知给玩家
function NotifyPlayer(playerId, message, type)
    type = type or 'info'
    TriggerClientEvent('esx:showNotification', playerId, message, type)
end

-- 发送通知给所有指定职业的玩家
function NotifyJob(jobs, message, type)
    local xPlayers = ESX.GetExtendedPlayers()
    
    for _, xPlayer in pairs(xPlayers) do
        for _, job in ipairs(jobs) do
            if xPlayer.job.name == job then
                NotifyPlayer(xPlayer.source, message, type)
                break
            end
        end
    end
end

-- 获取玩家坐标
function GetPlayerCoords(playerId)
    local ped = GetPlayerPed(playerId)
    return GetEntityCoords(ped)
end

-- 计算两点距离
function GetDistance(pos1, pos2)
    return #(vector3(pos1.x, pos1.y, pos1.z) - vector3(pos2.x, pos2.y, pos2.z))
end

-- 检查玩家是否在指定位置附近
function IsPlayerNearLocation(playerId, locations, maxDistance)
    local playerCoords = GetPlayerCoords(playerId)
    maxDistance = maxDistance or 5.0
    
    for _, location in ipairs(locations) do
        local distance = GetDistance(playerCoords, location)
        if distance <= maxDistance then
            return true, location
        end
    end
    
    return false
end

-- 给玩家添加道具
function GivePlayerItem(playerId, item, count, metadata)
    local xPlayer = ESX.GetPlayerFromId(playerId)
    if xPlayer then
        xPlayer.addInventoryItem(item, count or 1, metadata)
        return true
    end
    return false
end

-- 从玩家移除道具
function RemovePlayerItem(playerId, item, count)
    local xPlayer = ESX.GetPlayerFromId(playerId)
    if xPlayer then
        xPlayer.removeInventoryItem(item, count or 1)
        return true
    end
    return false
end

-- 检查玩家是否有道具
function HasPlayerItem(playerId, item, count)
    local xPlayer = ESX.GetPlayerFromId(playerId)
    if xPlayer then
        local itemCount = xPlayer.getInventoryItem(item).count
        return itemCount >= (count or 1)
    end
    return false
end

-- 获取在线玩家列表（排除自己）
function GetOnlinePlayersExcept(excludeId)
    local players = {}
    local allPlayers = ESX.GetExtendedPlayers()
    
    for _, xPlayer in pairs(allPlayers) do
        if xPlayer.source ~= excludeId then
            table.insert(players, {
                id = xPlayer.source,
                name = xPlayer.getName(),
                identifier = xPlayer.identifier
            })
        end
    end
    
    return players
end

-- 日志记录
function LogAction(action, playerId, targetId, details)
    local timestamp = os.date('%Y-%m-%d %H:%M:%S')
    local playerName = GetPlayerName(playerId) or 'Unknown'
    local targetName = targetId and GetPlayerName(targetId) or 'None'
    
    local logMessage = string.format('[%s] %s | Player: %s (%d) | Target: %s (%s) | Details: %s',
        timestamp, action, playerName, playerId, targetName, targetId or 'None', details or 'None')
    
    print(logMessage)
    
    -- 这里可以添加文件日志或数据库日志
end

-- 导出函数供其他模块使用
exports('GetPlayerIdentifierByServerId', GetPlayerIdentifierByServerId)
exports('HasPermission', HasPermission)
exports('NotifyPlayer', NotifyPlayer)
exports('NotifyJob', NotifyJob)
exports('GetPlayerCoords', GetPlayerCoords)
exports('GetDistance', GetDistance)
exports('IsPlayerNearLocation', IsPlayerNearLocation)
exports('GivePlayerItem', GivePlayerItem)
exports('RemovePlayerItem', RemovePlayerItem)
exports('HasPlayerItem', HasPlayerItem)
exports('GetOnlinePlayersExcept', GetOnlinePlayersExcept)
exports('LogAction', LogAction)

-- 全局变量导出
_G.activeDrugs = activeDrugs
_G.activeRescues = activeRescues
_G.surgeryInProgress = surgeryInProgress

print('^2[器官交易系统] ^7主服务器模块已加载')
