// 器官交易系统JavaScript

let currentTargetId = null;
let currentOrgans = {};
let currentOrganStatus = {};

// 监听NUI消息
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.type) {
        case 'openOrganSelection':
            openOrganSelection(data.targetId, data.organs, data.organStatus);
            break;
        case 'closeUI':
            closeAllUI();
            break;
        case 'showProgress':
            showProgressBar(data.duration, data.text);
            break;
        case 'hideProgress':
            hideProgressBar();
            break;
        case 'showNotification':
            showNotification(data.message, data.notificationType, data.duration);
            break;
        case 'showCountdown':
            showCountdown(data.duration, data.text);
            break;
        case 'hideCountdown':
            hideCountdown();
            break;
        case 'updateStatus':
            updatePlayerStatus(data.status);
            break;
        case 'showOrganStatus':
            showOrganStatus(data.organs);
            break;
    }
});

// 打开器官选择界面
function openOrganSelection(targetId, organs, organStatus) {
    currentTargetId = targetId;
    currentOrgans = organs;
    currentOrganStatus = organStatus;
    
    // 更新器官列表
    updateOrganList();
    
    // 更新器官按钮状态
    updateOrganButtons();
    
    // 显示界面
    document.getElementById('organSelection').style.display = 'block';
}

// 更新器官列表
function updateOrganList() {
    const organList = document.getElementById('organList');
    organList.innerHTML = '';
    
    for (const [key, organ] of Object.entries(currentOrgans)) {
        const isAvailable = currentOrganStatus[organ.item] === 1;
        
        const organItem = document.createElement('div');
        organItem.className = `organ-item ${isAvailable ? '' : 'unavailable'}`;
        organItem.innerHTML = `
            <div class="organ-name">[${key}] ${organ.name}</div>
            <div class="organ-price">$${organ.price.toLocaleString()}</div>
            <div class="organ-status ${isAvailable ? 'available' : 'unavailable'}">
                ${isAvailable ? '✓' : '✗'}
            </div>
        `;
        
        if (isAvailable) {
            organItem.style.cursor = 'pointer';
            organItem.onclick = () => selectOrgan(key);
        }
        
        organList.appendChild(organItem);
    }
}

// 更新器官按钮状态
function updateOrganButtons() {
    const organButtons = document.querySelectorAll('.organ-btn');
    
    organButtons.forEach(button => {
        const organKey = button.getAttribute('data-organ');
        const organ = currentOrgans[organKey];
        
        if (organ && currentOrganStatus[organ.item] === 1) {
            button.classList.remove('disabled');
            button.onclick = () => selectOrgan(organKey);
        } else {
            button.classList.add('disabled');
            button.onclick = null;
        }
    });
}

// 选择器官
function selectOrgan(organKey) {
    const organ = currentOrgans[organKey];
    if (!organ || currentOrganStatus[organ.item] !== 1) {
        return;
    }
    
    // 高亮选中的器官
    document.querySelectorAll('.organ-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
    
    const selectedButton = document.querySelector(`[data-organ="${organKey}"]`);
    if (selectedButton) {
        selectedButton.classList.add('selected');
    }
    
    // 确认对话框
    if (confirm(`确认提取 ${organ.name}？\n价值: $${organ.price.toLocaleString()}`)) {
        // 发送选择到Lua
        fetch(`https://${GetParentResourceName()}/selectOrgan`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                organ: organKey,
                targetId: currentTargetId
            })
        });
    }
}

// 取消手术
function cancelSurgery() {
    if (confirm('确认取消手术？')) {
        fetch(`https://${GetParentResourceName()}/cancelSurgery`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                targetId: currentTargetId
            })
        });
    }
}

// 关闭所有UI
function closeAllUI() {
    document.getElementById('organSelection').style.display = 'none';
    document.getElementById('progressBar').style.display = 'none';
    document.getElementById('countdown').style.display = 'none';
    document.getElementById('organStatus').style.display = 'none';
    
    // 清理选中状态
    document.querySelectorAll('.organ-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
}

// 显示进度条
function showProgressBar(duration, text) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const progressFill = document.getElementById('progressFill');
    const progressPercentage = document.getElementById('progressPercentage');
    
    progressText.textContent = text;
    progressBar.style.display = 'block';
    
    let startTime = Date.now();
    
    const updateProgress = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min((elapsed / duration) * 100, 100);
        
        progressFill.style.width = progress + '%';
        progressPercentage.textContent = Math.round(progress) + '%';
        
        if (progress < 100) {
            requestAnimationFrame(updateProgress);
        } else {
            setTimeout(() => {
                hideProgressBar();
            }, 500);
        }
    };
    
    updateProgress();
}

// 隐藏进度条
function hideProgressBar() {
    document.getElementById('progressBar').style.display = 'none';
}

// 显示通知
function showNotification(message, type, duration) {
    const container = document.getElementById('notifications');
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    container.appendChild(notification);
    
    // 自动移除通知
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, duration);
}

// 显示倒计时
function showCountdown(duration, text) {
    const countdown = document.getElementById('countdown');
    const countdownText = document.getElementById('countdownText');
    const countdownTime = document.getElementById('countdownTime');
    
    countdownText.textContent = text;
    countdown.style.display = 'block';
    
    let endTime = Date.now() + duration;
    
    const updateCountdown = () => {
        const remaining = Math.max(0, endTime - Date.now());
        const minutes = Math.floor(remaining / 60000);
        const seconds = Math.floor((remaining % 60000) / 1000);
        
        countdownTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        if (remaining > 0) {
            requestAnimationFrame(updateCountdown);
        } else {
            hideCountdown();
        }
    };
    
    updateCountdown();
}

// 隐藏倒计时
function hideCountdown() {
    document.getElementById('countdown').style.display = 'none';
}

// 更新玩家状态
function updatePlayerStatus(status) {
    const drugStatus = document.querySelector('#drugStatus .status-value');
    const surgeryStatus = document.querySelector('#surgeryStatus .status-value');
    const lifeStatus = document.querySelector('#lifeStatus .status-value');
    
    drugStatus.textContent = status.drugged ? '被迷晕' : '正常';
    drugStatus.style.color = status.drugged ? '#ff4444' : '#00ff00';
    
    surgeryStatus.textContent = status.inSurgery ? '手术中' : '无';
    surgeryStatus.style.color = status.inSurgery ? '#ff4444' : '#00ff00';
    
    lifeStatus.textContent = status.lifeExtended ? '延长中' : '正常';
    lifeStatus.style.color = status.lifeExtended ? '#ffff00' : '#00ff00';
}

// 显示器官状态
function showOrganStatus(organs) {
    const container = document.getElementById('organStatus');
    const content = document.getElementById('organStatusContent');
    
    content.innerHTML = '';
    
    for (const [organField, status] of Object.entries(organs)) {
        if (organField === 'id' || organField === 'identifier' || organField === 'last_updated') {
            continue;
        }
        
        // 获取器官名称
        let organName = organField;
        for (const organ of Object.values(currentOrgans)) {
            if (organ.item === organField) {
                organName = organ.name;
                break;
            }
        }
        
        const statusItem = document.createElement('div');
        statusItem.className = 'organ-status-item';
        statusItem.innerHTML = `
            <span class="organ-status-name">${organName}</span>
            <span class="organ-status-indicator ${status === 1 ? 'healthy' : 'damaged'}">
                ${status === 1 ? '✓' : '✗'}
            </span>
        `;
        
        content.appendChild(statusItem);
    }
    
    container.style.display = 'block';
}

// 隐藏器官状态
function hideOrganStatus() {
    document.getElementById('organStatus').style.display = 'none';
}

// ESC键处理
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        fetch(`https://${GetParentResourceName()}/closeUI`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });
    }
});

// 获取资源名称
function GetParentResourceName() {
    return window.location.hostname;
}
