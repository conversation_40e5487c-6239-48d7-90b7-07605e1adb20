# 器官交易系统安装指南

## 系统要求

- FiveM服务器
- ESX Framework (最新版本)
- oxmysql 资源
- MySQL/MariaDB 数据库

## 详细安装步骤

### 1. 下载和放置文件

1. 将整个插件文件夹重命名为 `organ_trade`
2. 放置到你的 `resources` 目录下
3. 确保文件结构如下：
   ```
   resources/
   └── organ_trade/
       ├── fxmanifest.lua
       ├── config.lua
       ├── server/
       ├── client/
       ├── html/
       ├── items.sql
       └── README.md
   ```

### 2. 数据库配置

1. 打开你的数据库管理工具（如phpMyAdmin）
2. 选择你的ESX数据库
3. 执行 `items.sql` 文件中的SQL语句来添加道具

**对于标准ESX：**
```sql
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('drug_knockout', '迷药', 1, 0, 1),
('surgery_knife', '手术刀', 2, 0, 1),
('adrenaline', '肾上腺素', 1, 0, 1),
-- ... 其他道具
```

**对于ox_inventory：**
```sql
INSERT INTO `ox_inventory`.`items` (`name`, `label`, `weight`, `stack`, `close`, `description`) VALUES
('drug_knockout', '迷药', 100, 1, 1, '一种强效迷药，可以迷晕目标'),
-- ... 其他道具
```

### 3. 服务器配置

在你的 `server.cfg` 文件中添加：
```
ensure organ_trade
```

**重要：** 确保在ESX和oxmysql之后加载：
```
ensure es_extended
ensure oxmysql
ensure organ_trade
```

### 4. 权限配置

编辑 `config.lua` 文件中的权限设置：

```lua
Config.Permissions = {
    use_drug = {'gang'},                    -- 可使用迷药的职业/帮派
    perform_surgery = {'gang'},             -- 可进行解剖的职业/帮派
    medical_response = {'ambulance', 'doctor'} -- 医疗响应职业
}
```

根据你的服务器设置调整职业名称。

### 5. 位置配置

在 `config.lua` 中设置解剖地点和医院位置：

```lua
-- 解剖地点（根据你的地图调整坐标）
Config.Surgery.locations = {
    {x = 123.45, y = -678.90, z = 28.30, name = '废弃仓库'},
    {x = 234.56, y = -789.01, z = 35.40, name = '地下室'}
}

-- 医院位置
Config.Medical.hospital_locations = {
    {x = 298.67, y = -584.23, z = 43.26, name = '中央医院'},
    {x = 1839.6, y = 3672.93, z = 34.28, name = '沙漠医院'}
}
```

### 6. 图像文件（可选）

1. 在 `html/images/` 目录下放置 `human_body.png` 文件
2. 这是器官选择界面的人体图像
3. 如果没有图像，系统仍可正常运行

### 7. 重启服务器

完成所有配置后，重启你的FiveM服务器。

## 验证安装

### 检查控制台

启动服务器后，检查控制台是否有以下消息：
```
[器官交易系统] 数据库表初始化完成
[器官交易系统] 主服务器模块已加载
[器官交易系统] 迷药系统模块已加载
[器官交易系统] 解剖系统模块已加载
[器官交易系统] 求救系统模块已加载
[器官交易系统] 医疗救治系统模块已加载
```

### 游戏内测试

1. 进入游戏
2. 按 `F6` 应该显示器官交易菜单
3. 按 `F7` 应该显示医护菜单（如果你有权限）
4. 输入 `/sos` 测试求救系统

### 道具测试

使用管理员命令给自己道具：
```
/giveitem [玩家ID] drug_knockout 1
/giveitem [玩家ID] surgery_knife 1
/giveitem [玩家ID] adrenaline 1
```

## 常见问题解决

### 问题1：道具无法使用
**解决方案：**
- 检查是否正确导入了道具SQL
- 确认道具名称与配置文件一致
- 检查玩家权限设置

### 问题2：数据库错误
**解决方案：**
- 确保oxmysql正确安装和配置
- 检查数据库连接字符串
- 确认数据库用户有创建表的权限

### 问题3：UI界面不显示
**解决方案：**
- 检查html文件路径是否正确
- 确认fxmanifest.lua中的文件列表
- 检查浏览器控制台错误

### 问题4：权限问题
**解决方案：**
- 检查Config.Permissions设置
- 确认玩家职业名称正确
- 测试时可以临时设置为所有人可用

### 问题5：坐标问题
**解决方案：**
- 使用游戏内坐标获取工具
- 检查Z坐标（高度）是否正确
- 确保坐标在可访问的区域

## 调试模式

如果遇到问题，可以启用调试模式：

在 `config.lua` 中设置：
```lua
Config.Debug = true
```

这将在控制台和游戏内显示详细的调试信息。

## 性能优化

### 服务器性能
- 定期清理过期的数据库记录
- 调整检查间隔时间
- 限制同时进行的手术数量

### 客户端性能
- 调整UI更新频率
- 优化图像文件大小
- 减少不必要的动画效果

## 备份建议

在安装前，建议备份：
1. 数据库
2. 现有的资源文件
3. server.cfg配置

这样如果出现问题可以快速恢复。

## 技术支持

如果按照本指南安装后仍有问题，请检查：
1. 服务器控制台错误信息
2. 客户端F8控制台错误
3. 数据库连接状态
4. 资源加载顺序

记录详细的错误信息有助于快速解决问题。
