# 图像文件说明

## 需要的图像文件

请在此目录下放置以下图像文件：

### human_body.png
- 人体轮廓图，用于器官选择界面
- 建议尺寸：300x500像素
- 格式：PNG（支持透明背景）
- 用途：作为器官选择的背景图

## 图像要求

1. **human_body.png**
   - 清晰的人体正面轮廓
   - 简洁的线条风格
   - 透明或深色背景
   - 器官位置清晰可见

## 获取图像

你可以：
1. 使用免费的医学图像资源
2. 创建简单的人体轮廓图
3. 使用AI生成工具创建
4. 从医学教育网站获取（注意版权）

## 临时解决方案

如果暂时没有图像文件，系统仍然可以正常运行，只是器官选择界面会显示占位符。

器官选择按钮的位置是通过CSS定位的，可以根据实际图像调整位置：

```css
.organ-btn[data-organ="A"] { top: 20%; left: 45%; } /* 心脏 */
.organ-btn[data-organ="B"] { top: 35%; left: 40%; } /* 肝脏 */
/* 等等... */
```
