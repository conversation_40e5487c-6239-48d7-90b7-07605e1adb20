-- 器官交易系统主客户端文件

ESX = exports["es_extended"]:getSharedObject()

local isSystemReady = false

-- 系统初始化
CreateThread(function()
    while not ESX.IsPlayerLoaded() do
        Wait(100)
    end
    
    Wait(2000) -- 等待其他系统加载
    isSystemReady = true
    
    if Config.Debug then
        print('^2[器官交易系统] ^7客户端系统已就绪')
    end
end)

-- 道具使用处理
CreateThread(function()
    while true do
        if isSystemReady then
            -- 检查迷药道具使用
            if ESX.IsPlayerLoaded() then
                local playerData = ESX.GetPlayerData()
                
                -- 这里可以添加道具使用检测逻辑
                -- 由于ESX的道具使用通常通过服务器事件处理，这里主要用于UI交互
            end
        end
        
        Wait(1000)
    end
end)

-- 键盘快捷键
CreateThread(function()
    while true do
        if isSystemReady then
            -- F6 - 器官交易菜单（仅限有权限的玩家）
            if IsControlJustPressed(0, 167) then -- F6
                OpenOrganTradeMenu()
            end
            
            -- F7 - 医护菜单（仅限医护人员）
            if IsControlJustPressed(0, 168) then -- F7
                OpenMedicalMenu()
            end
        end
        
        Wait(0)
    end
end)

-- 打开器官交易菜单
function OpenOrganTradeMenu()
    local elements = {
        {label = '使用迷药', value = 'use_drug'},
        {label = '进行解剖', value = 'surgery'},
        {label = '查看库存', value = 'inventory'},
        {label = '关闭菜单', value = 'close'}
    }
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'organ_trade_menu', {
        title = '器官交易系统',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        local action = data.current.value
        
        if action == 'use_drug' then
            exports['organ_trade']:UseDrugItem()
        elseif action == 'surgery' then
            exports['organ_trade']:UseSurgeryKnife()
        elseif action == 'inventory' then
            -- 这里可以添加库存查看功能
            ESX.ShowNotification('库存功能开发中...', 'info')
        elseif action == 'close' then
            menu.close()
        end
    end, function(data, menu)
        menu.close()
    end)
end

-- 打开医护菜单
function OpenMedicalMenu()
    local elements = {
        {label = '查看求救信号', value = 'view_rescues'},
        {label = '使用手术台', value = 'surgery_table'},
        {label = '医护工具', value = 'medical_tools'},
        {label = '关闭菜单', value = 'close'}
    }
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'medical_menu', {
        title = '医护系统',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        local action = data.current.value
        
        if action == 'view_rescues' then
            exports['organ_trade']:ViewActiveRescues()
        elseif action == 'surgery_table' then
            exports['organ_trade']:UseSurgeryTable()
        elseif action == 'medical_tools' then
            -- 这里可以添加医护工具菜单
            ESX.ShowNotification('医护工具功能开发中...', 'info')
        elseif action == 'close' then
            menu.close()
        end
    end, function(data, menu)
        menu.close()
    end)
end

-- 状态显示
CreateThread(function()
    while true do
        if isSystemReady and Config.Debug then
            local playerPed = PlayerPedId()
            local coords = GetEntityCoords(playerPed)
            
            -- 显示调试信息
            SetTextFont(4)
            SetTextProportional(1)
            SetTextScale(0.35, 0.35)
            SetTextColour(255, 255, 255, 255)
            SetTextDropShadow(0, 0, 0, 0, 255)
            SetTextEdge(1, 0, 0, 0, 255)
            SetTextDropShadow()
            SetTextOutline()
            SetTextEntry("STRING")
            
            local debugText = string.format(
                "器官交易系统调试信息:\n" ..
                "坐标: %.2f, %.2f, %.2f\n" ..
                "是否被迷晕: %s\n" ..
                "是否在手术中: %s\n" ..
                "生命延长: %s\n" ..
                "可发送SOS: %s",
                coords.x, coords.y, coords.z,
                exports['organ_trade']:IsDrugged() and "是" or "否",
                exports['organ_trade']:IsInSurgery() and "是" or "否",
                exports['organ_trade']:IsLifeExtended() and "是" or "否",
                exports['organ_trade']:CanSendSOS() and "是" or "否"
            )
            
            AddTextComponentString(debugText)
            DrawText(0.01, 0.3)
        end
        
        Wait(100)
    end
end)

-- 帮助文本
CreateThread(function()
    while true do
        if isSystemReady then
            local playerPed = PlayerPedId()
            
            -- 显示按键提示
            SetTextComponentFormat('STRING')
            AddTextComponentString('~INPUT_SELECT_CHARACTER_FRANKLIN~ 器官交易菜单  ~INPUT_SELECT_CHARACTER_MICHAEL~ 医护菜单')
            DisplayHelpTextFromStringLabel(0, 0, 1, -1)
        end
        
        Wait(1000)
    end
end)

-- 添加聊天建议
CreateThread(function()
    Wait(5000) -- 等待聊天系统加载
    
    TriggerEvent('chat:addSuggestion', '/sos', '发送求救信号')
    TriggerEvent('chat:addSuggestion', '/rescues', '查看活跃求救信号（医护人员）')
    TriggerEvent('chat:addSuggestion', '/surgery', '使用手术台（医护人员）')
end)

-- 导出函数
exports('OpenOrganTradeMenu', OpenOrganTradeMenu)
exports('OpenMedicalMenu', OpenMedicalMenu)
exports('IsSystemReady', function() return isSystemReady end)

print('^2[器官交易系统] ^7主客户端模块已加载')
