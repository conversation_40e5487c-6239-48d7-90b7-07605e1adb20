-- UI界面处理

local uiOpen = false
local currentMenu = nil

-- 打开人体器官选择UI
function OpenOrganSelectionUI(targetId, organStatus)
    if uiOpen then return end
    
    uiOpen = true
    SetNuiFocus(true, true)
    
    SendNUIMessage({
        type = 'openOrganSelection',
        targetId = targetId,
        organs = Config.Surgery.organs,
        organStatus = organStatus
    })
end

-- 关闭UI
function CloseUI()
    if not uiOpen then return end
    
    uiOpen = false
    SetNuiFocus(false, false)
    
    SendNUIMessage({
        type = 'closeUI'
    })
end

-- NUI回调处理
RegisterNUICallback('selectOrgan', function(data, cb)
    local organKey = data.organ
    local targetId = data.targetId
    
    if organKey and targetId then
        TriggerServerEvent('organ_trade:extractOrgan', targetId, organKey)
    end
    
    CloseUI()
    cb('ok')
end)

RegisterNUICallback('cancelSurgery', function(data, cb)
    local targetId = data.targetId
    
    if targetId then
        TriggerServerEvent('organ_trade:cancelSurgery', targetId)
    end
    
    CloseUI()
    cb('ok')
end)

RegisterNUICallback('closeUI', function(data, cb)
    CloseUI()
    cb('ok')
end)

-- ESC键关闭UI
CreateThread(function()
    while true do
        if uiOpen then
            if IsControlJustPressed(0, 322) then -- ESC
                CloseUI()
            end
        end
        Wait(100)
    end
end)

-- 显示进度条
function ShowProgressBar(duration, text)
    SendNUIMessage({
        type = 'showProgress',
        duration = duration,
        text = text or '处理中...'
    })
end

-- 隐藏进度条
function HideProgressBar()
    SendNUIMessage({
        type = 'hideProgress'
    })
end

-- 显示通知
function ShowNotification(message, type, duration)
    SendNUIMessage({
        type = 'showNotification',
        message = message,
        notificationType = type or 'info',
        duration = duration or 5000
    })
end

-- 显示倒计时
function ShowCountdown(duration, text)
    SendNUIMessage({
        type = 'showCountdown',
        duration = duration,
        text = text or '倒计时'
    })
end

-- 隐藏倒计时
function HideCountdown()
    SendNUIMessage({
        type = 'hideCountdown'
    })
end

-- 更新玩家状态显示
function UpdatePlayerStatus(status)
    SendNUIMessage({
        type = 'updateStatus',
        status = status
    })
end

-- 显示器官状态
function ShowOrganStatus(organs)
    SendNUIMessage({
        type = 'showOrganStatus',
        organs = organs
    })
end

-- 导出函数
exports('OpenOrganSelectionUI', OpenOrganSelectionUI)
exports('CloseUI', CloseUI)
exports('ShowProgressBar', ShowProgressBar)
exports('HideProgressBar', HideProgressBar)
exports('ShowNotification', ShowNotification)
exports('ShowCountdown', ShowCountdown)
exports('HideCountdown', HideCountdown)
exports('UpdatePlayerStatus', UpdatePlayerStatus)
exports('ShowOrganStatus', ShowOrganStatus)

print('^2[器官交易系统] ^7UI模块已加载')
