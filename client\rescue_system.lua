-- 求救系统客户端

local canSendSOS = false
local rescueWaypoint = nil
local currentRescueCall = nil

-- 启用SOS功能
RegisterNetEvent('organ_trade:enableSOS', function()
    canSendSOS = true
    ESX.ShowNotification('你现在可以发送求救信号 /sos', 'info')
end)

-- 发送SOS命令
RegisterCommand(Config.Rescue.command, function()
    if not canSendSOS then
        ESX.ShowNotification('你现在无法发送求救信号', 'error')
        return
    end
    
    -- 确认对话框
    ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sos_confirm', {
        title = '确认发送求救信号？'
    }, function(data, menu)
        if data.value == 'yes' then
            TriggerServerEvent('organ_trade:sendSOS')
            canSendSOS = false -- 防止重复发送
        end
        menu.close()
    end, function(data, menu)
        menu.close()
    end)
end)

-- 接收SOS求救信号（医护人员）
RegisterNetEvent('organ_trade:receiveSOSCall', function(callData)
    -- 创建地图标记
    local blip = AddBlipForCoord(callData.coords.x, callData.coords.y, callData.coords.z)
    SetBlipSprite(blip, Config.Rescue.blip.sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, Config.Rescue.blip.scale)
    SetBlipColour(blip, Config.Rescue.blip.color)
    SetBlipAsShortRange(blip, false)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentString(string.format('SOS - %s', callData.victimName))
    EndTextCommandSetBlipName(blip)
    
    -- 闪烁效果
    SetBlipFlashes(blip, true)
    
    -- 显示通知
    ESX.ShowNotification(string.format('收到求救信号！\n受害者: %s\n时间: %s', callData.victimName, callData.timestamp), 'error')
    
    -- 播放警报声
    PlaySoundFrontend(-1, 'TIMER_STOP', 'HUD_MINI_GAME_SOUNDSET', 1)
    
    -- 5分钟后自动移除标记
    CreateThread(function()
        Wait(Config.Rescue.response_time)
        if DoesBlipExist(blip) then
            RemoveBlip(blip)
        end
    end)
end)

-- 查看活跃求救列表（医护人员）
function ViewActiveRescues()
    TriggerServerEvent('organ_trade:getActiveRescues')
end

-- 接收活跃求救列表
RegisterNetEvent('organ_trade:receiveActiveRescues', function(rescues)
    if #rescues == 0 then
        ESX.ShowNotification('当前没有活跃的求救信号', 'info')
        return
    end
    
    local elements = {}
    
    for _, rescue in ipairs(rescues) do
        local timeText = string.format('%d秒前', rescue.timeAgo)
        if rescue.timeAgo >= 60 then
            timeText = string.format('%d分钟前', math.floor(rescue.timeAgo / 60))
        end
        
        table.insert(elements, {
            label = string.format('%s (%s)', rescue.victimName, timeText),
            value = rescue.id,
            coords = rescue.coords
        })
    end
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'active_rescues_menu', {
        title = '活跃求救信号',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        local callId = data.current.value
        local coords = data.current.coords
        
        menu.close()
        
        -- 确认响应
        ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'respond_confirm', {
            title = '确认响应该求救信号？'
        }, function(data2, menu2)
            if data2.value == 'yes' then
                TriggerServerEvent('organ_trade:respondToSOS', callId)
                currentRescueCall = callId
            end
            menu2.close()
        end, function(data2, menu2)
            menu2.close()
        end)
        
    end, function(data, menu)
        menu.close()
    end)
end)

-- 设置救援导航点
RegisterNetEvent('organ_trade:setRescueWaypoint', function(coords, victimName)
    -- 移除旧的导航点
    if rescueWaypoint then
        RemoveBlip(rescueWaypoint)
    end
    
    -- 创建新的导航点
    rescueWaypoint = AddBlipForCoord(coords.x, coords.y, coords.z)
    SetBlipSprite(rescueWaypoint, 8)
    SetBlipDisplay(rescueWaypoint, 4)
    SetBlipScale(rescueWaypoint, 1.2)
    SetBlipColour(rescueWaypoint, 2)
    SetBlipRoute(rescueWaypoint, true)
    SetBlipRouteColour(rescueWaypoint, 2)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentString(string.format('救援目标 - %s', victimName))
    EndTextCommandSetBlipName(rescueWaypoint)
    
    ESX.ShowNotification('导航点已设置，请前往救援现场', 'success')
    
    -- 检查到达现场
    CreateThread(function()
        while rescueWaypoint and DoesBlipExist(rescueWaypoint) do
            local playerCoords = GetEntityCoords(PlayerPedId())
            local distance = GetDistance(playerCoords, coords)
            
            if distance <= 10.0 then
                -- 到达现场
                RemoveBlip(rescueWaypoint)
                rescueWaypoint = nil
                
                ESX.ShowNotification('你已到达救援现场', 'success')
                TriggerServerEvent('organ_trade:arriveAtScene', currentRescueCall)
                break
            end
            
            Wait(1000)
        end
    end)
end)

-- 显示救援选项
RegisterNetEvent('organ_trade:showRescueOptions', function(callId, victimId)
    local elements = {
        {label = '使用肾上腺素', value = 'adrenaline'},
        {label = '带回医院', value = 'transport'},
        {label = '取消救援', value = 'cancel'}
    }
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'rescue_options_menu', {
        title = '救援选项',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        local action = data.current.value
        
        if action == 'adrenaline' then
            menu.close()
            TriggerServerEvent('organ_trade:useAdrenaline', callId, victimId)
        elseif action == 'transport' then
            menu.close()
            TriggerServerEvent('organ_trade:transportToHospital', callId, victimId)
        elseif action == 'cancel' then
            menu.close()
            currentRescueCall = nil
        end
    end, function(data, menu)
        menu.close()
        currentRescueCall = nil
    end)
end)

-- 医护人员命令
RegisterCommand('rescues', function()
    ViewActiveRescues()
end)

-- 添加建议文本
TriggerEvent('chat:addSuggestion', '/sos', '发送求救信号')
TriggerEvent('chat:addSuggestion', '/rescues', '查看活跃求救信号（医护人员）')

-- 导出函数
exports('ViewActiveRescues', ViewActiveRescues)
exports('CanSendSOS', function() return canSendSOS end)

print('^2[器官交易系统] ^7求救系统客户端已加载')
