-- 器官交易系统测试命令
-- 这个文件包含用于测试系统功能的管理员命令
-- 在生产环境中请移除或注释掉这些命令

-- 测试给予道具
RegisterCommand('giveorgantestitem', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    -- 检查管理员权限
    if xPlayer.getGroup() ~= 'admin' then
        TriggerClientEvent('esx:showNotification', source, '你没有权限使用此命令', 'error')
        return
    end
    
    local itemType = args[1]
    local targetId = tonumber(args[2]) or source
    local xTarget = ESX.GetPlayerFromId(targetId)
    
    if not xTarget then
        TriggerClientEvent('esx:showNotification', source, '目标玩家不存在', 'error')
        return
    end
    
    local items = {
        drug = 'drug_knockout',
        knife = 'surgery_knife',
        adrenaline = 'adrenaline',
        heart = 'organ_heart',
        liver = 'organ_liver',
        kidney = 'organ_kidney',
        lung = 'organ_lung',
        eye = 'organ_eye',
        pancreas = 'organ_pancreas',
        spleen = 'organ_spleen'
    }
    
    if itemType and items[itemType] then
        xTarget.addInventoryItem(items[itemType], 1)
        TriggerClientEvent('esx:showNotification', source, string.format('给予 %s %s', xTarget.getName(), items[itemType]), 'success')
        TriggerClientEvent('esx:showNotification', targetId, string.format('你获得了 %s', items[itemType]), 'success')
    else
        TriggerClientEvent('esx:showNotification', source, '用法: /giveorgantestitem [drug|knife|adrenaline|heart|liver|kidney|lung|eye|pancreas|spleen] [玩家ID]', 'info')
    end
end, false)

-- 测试重置玩家器官状态
RegisterCommand('resetorgans', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    -- 检查管理员权限
    if xPlayer.getGroup() ~= 'admin' then
        TriggerClientEvent('esx:showNotification', source, '你没有权限使用此命令', 'error')
        return
    end
    
    local targetId = tonumber(args[1]) or source
    local xTarget = ESX.GetPlayerFromId(targetId)
    
    if not xTarget then
        TriggerClientEvent('esx:showNotification', source, '目标玩家不存在', 'error')
        return
    end
    
    local identifier = xTarget.identifier
    
    -- 重置所有器官为健康状态
    MySQL.query('UPDATE player_organs SET organ_heart = 1, organ_liver = 1, organ_kidney = 1, organ_lung = 1, organ_eye = 1, organ_pancreas = 1, organ_spleen = 1 WHERE identifier = ?', {identifier})
    
    TriggerClientEvent('esx:showNotification', source, string.format('已重置 %s 的器官状态', xTarget.getName()), 'success')
    TriggerClientEvent('esx:showNotification', targetId, '你的器官状态已被重置为健康', 'success')
end, false)

-- 测试清除解剖冷却
RegisterCommand('clearsurgerycooldown', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    -- 检查管理员权限
    if xPlayer.getGroup() ~= 'admin' then
        TriggerClientEvent('esx:showNotification', source, '你没有权限使用此命令', 'error')
        return
    end
    
    local targetId = tonumber(args[1]) or source
    local xTarget = ESX.GetPlayerFromId(targetId)
    
    if not xTarget then
        TriggerClientEvent('esx:showNotification', source, '目标玩家不存在', 'error')
        return
    end
    
    local identifier = xTarget.identifier
    
    -- 清除冷却时间
    MySQL.query('DELETE FROM surgery_cooldowns WHERE victim_identifier = ?', {identifier})
    
    TriggerClientEvent('esx:showNotification', source, string.format('已清除 %s 的解剖冷却', xTarget.getName()), 'success')
    TriggerClientEvent('esx:showNotification', targetId, '你的解剖冷却已被清除', 'success')
end, false)

-- 测试传送到解剖地点
RegisterCommand('tpsurgery', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    -- 检查管理员权限
    if xPlayer.getGroup() ~= 'admin' then
        TriggerClientEvent('esx:showNotification', source, '你没有权限使用此命令', 'error')
        return
    end
    
    local locationIndex = tonumber(args[1]) or 1
    local location = Config.Surgery.locations[locationIndex]
    
    if not location then
        TriggerClientEvent('esx:showNotification', source, '无效的地点索引', 'error')
        return
    end
    
    TriggerClientEvent('organ_trade:teleportPlayer', source, location)
    TriggerClientEvent('esx:showNotification', source, string.format('传送到 %s', location.name), 'success')
end, false)

-- 测试传送到医院
RegisterCommand('tphospital', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    -- 检查管理员权限
    if xPlayer.getGroup() ~= 'admin' then
        TriggerClientEvent('esx:showNotification', source, '你没有权限使用此命令', 'error')
        return
    end
    
    local hospitalIndex = tonumber(args[1]) or 1
    local hospital = Config.Medical.hospital_locations[hospitalIndex]
    
    if not hospital then
        TriggerClientEvent('esx:showNotification', source, '无效的医院索引', 'error')
        return
    end
    
    TriggerClientEvent('organ_trade:teleportToHospital', source, hospital)
    TriggerClientEvent('esx:showNotification', source, string.format('传送到 %s', hospital.name), 'success')
end, false)

-- 测试强制迷药效果
RegisterCommand('forcedrug', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    -- 检查管理员权限
    if xPlayer.getGroup() ~= 'admin' then
        TriggerClientEvent('esx:showNotification', source, '你没有权限使用此命令', 'error')
        return
    end
    
    local targetId = tonumber(args[1]) or source
    local xTarget = ESX.GetPlayerFromId(targetId)
    
    if not xTarget then
        TriggerClientEvent('esx:showNotification', source, '目标玩家不存在', 'error')
        return
    end
    
    -- 强制应用迷药效果
    exports['organ_trade']:ApplyDrugEffect(targetId)
    
    TriggerClientEvent('esx:showNotification', source, string.format('对 %s 应用迷药效果', xTarget.getName()), 'success')
    TriggerClientEvent('esx:showNotification', targetId, '你被强制应用了迷药效果', 'error')
end, false)

-- 测试查看玩家器官状态
RegisterCommand('checkorgans', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    -- 检查管理员权限
    if xPlayer.getGroup() ~= 'admin' then
        TriggerClientEvent('esx:showNotification', source, '你没有权限使用此命令', 'error')
        return
    end
    
    local targetId = tonumber(args[1]) or source
    local xTarget = ESX.GetPlayerFromId(targetId)
    
    if not xTarget then
        TriggerClientEvent('esx:showNotification', source, '目标玩家不存在', 'error')
        return
    end
    
    local identifier = xTarget.identifier
    local organs = GetPlayerOrgans(identifier)
    
    local organNames = {
        organ_heart = '心脏',
        organ_liver = '肝脏',
        organ_kidney = '肾脏',
        organ_lung = '肺部',
        organ_eye = '眼球',
        organ_pancreas = '胰腺',
        organ_spleen = '脾脏'
    }
    
    local statusText = string.format('%s 的器官状态:\n', xTarget.getName())
    for organField, status in pairs(organs) do
        if organNames[organField] then
            local statusStr = status == 1 and '健康' or '缺失'
            statusText = statusText .. string.format('%s: %s\n', organNames[organField], statusStr)
        end
    end
    
    TriggerClientEvent('esx:showNotification', source, statusText, 'info')
end, false)

-- 显示测试命令帮助
RegisterCommand('organtest', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    -- 检查管理员权限
    if xPlayer.getGroup() ~= 'admin' then
        TriggerClientEvent('esx:showNotification', source, '你没有权限使用此命令', 'error')
        return
    end
    
    local helpText = [[
器官交易系统测试命令:
/giveorgantestitem [类型] [玩家ID] - 给予测试道具
/resetorgans [玩家ID] - 重置器官状态
/clearsurgerycooldown [玩家ID] - 清除解剖冷却
/tpsurgery [地点索引] - 传送到解剖地点
/tphospital [医院索引] - 传送到医院
/forcedrug [玩家ID] - 强制迷药效果
/checkorgans [玩家ID] - 查看器官状态

道具类型: drug, knife, adrenaline, heart, liver, kidney, lung, eye, pancreas, spleen
]]
    
    TriggerClientEvent('esx:showNotification', source, helpText, 'info')
end, false)

print('^3[器官交易系统] ^7测试命令已加载 - 使用 /organtest 查看帮助')
