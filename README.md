# FiveM 器官交易系统 (ESX)

一个完整的FiveM器官交易插件，支持迷药、解剖、求救、医疗救治等功能。

## 功能特性

### 🧪 迷药系统
- 使用迷药可以迷晕玩家5秒，然后药效发作
- 迷晕效果类似压力满了的状态
- 被迷晕的玩家可以进行缓慢爬行
- 视觉效果和移动限制

### 🔪 解剖系统
- 需要购买手术刀道具才能进行解剖
- 迷晕后可带到指定地方进行解剖
- 使用人体平面图选择解剖器官(A-G选项)
- 解剖下来的物品为固定物品
- 一小时内同玩家只能被解剖一次

### 🆘 求救系统
- 被解剖的玩家可发起求救信号 `/sos`
- 医护人员接收求救信号
- 5分钟倒计时响应机制
- 地图标记和导航系统

### 🏥 医疗救治系统
- 医护到达现场使用肾上腺素延长患者生命
- 回到医院使用手术台进行治疗
- 医院具有修复坏掉的器官功能
- 完整的医疗流程

### 🛡️ 防滥用机制
- 一小时内同玩家只能被解剖一次
- 权限系统控制功能使用
- 冷却时间和状态检查

## 安装说明

### 1. 依赖项
- ESX Framework
- oxmysql (MySQL数据库)

### 2. 安装步骤

1. 将插件文件夹放入 `resources` 目录
2. 在 `server.cfg` 中添加：
   ```
   ensure organ_trade
   ```
3. 导入数据库文件：
   ```sql
   -- 执行 items.sql 添加道具
   ```
4. 重启服务器

### 3. 配置

编辑 `config.lua` 文件来自定义：
- 迷药效果时间和强度
- 解剖地点坐标
- 器官价格和类型
- 医院位置
- 权限设置

## 使用方法

### 基础操作
- `F6` - 打开器官交易菜单
- `F7` - 打开医护菜单
- `/sos` - 发送求救信号
- `/rescues` - 查看求救信号(医护)
- `/surgery` - 使用手术台(医护)

### 器官交易流程
1. 获取迷药道具
2. 对目标玩家使用迷药
3. 等待5秒药效发作
4. 将迷晕玩家带到解剖地点
5. 使用手术刀开始解剖
6. 选择要提取的器官
7. 完成解剖获得器官道具

### 医疗救治流程
1. 受害者发送SOS求救
2. 医护人员响应求救
3. 前往现场使用肾上腺素
4. 将患者送回医院
5. 使用手术台修复器官

## 配置选项

### 迷药配置
```lua
Config.Drug = {
    item = 'drug_knockout',      -- 迷药道具名称
    duration = 5000,             -- 迷晕持续时间
    effect_delay = 5000,         -- 药效发作延迟
    crawl_speed = 0.3,          -- 爬行速度倍数
    use_distance = 3.0          -- 使用距离
}
```

### 器官配置
```lua
Config.Surgery = {
    organs = {
        A = {name = '心脏', item = 'organ_heart', price = 50000},
        B = {name = '肝脏', item = 'organ_liver', price = 30000},
        -- 更多器官...
    },
    cooldown = 3600000,         -- 1小时冷却
    surgery_time = 30000        -- 解剖时间
}
```

### 权限配置
```lua
Config.Permissions = {
    use_drug = {'gang'},                    -- 可使用迷药
    perform_surgery = {'gang'},             -- 可进行解剖
    medical_response = {'ambulance', 'doctor'} -- 医疗响应
}
```

## 道具列表

| 道具名称 | 标签 | 用途 |
|---------|------|------|
| drug_knockout | 迷药 | 迷晕目标玩家 |
| surgery_knife | 手术刀 | 进行解剖手术 |
| adrenaline | 肾上腺素 | 延长生命 |
| organ_heart | 心脏 | 解剖获得的器官 |
| organ_liver | 肝脏 | 解剖获得的器官 |
| organ_kidney | 肾脏 | 解剖获得的器官 |
| organ_lung | 肺部 | 解剖获得的器官 |
| organ_eye | 眼球 | 解剖获得的器官 |
| organ_pancreas | 胰腺 | 解剖获得的器官 |
| organ_spleen | 脾脏 | 解剖获得的器官 |

## 数据库表

系统会自动创建以下数据库表：
- `organ_trades` - 器官交易记录
- `surgery_cooldowns` - 解剖冷却记录
- `rescue_calls` - 求救记录
- `player_organs` - 玩家器官状态

## 故障排除

### 常见问题

1. **道具无法使用**
   - 检查是否正确导入了道具SQL
   - 确认玩家有相应权限

2. **数据库错误**
   - 确保oxmysql正确安装
   - 检查数据库连接配置

3. **UI界面不显示**
   - 检查html文件是否正确放置
   - 确认NUI资源正常加载

### 调试模式

在 `config.lua` 中设置：
```lua
Config.Debug = true
```

这将显示详细的调试信息。

## 更新日志

### v1.0.0
- 初始版本发布
- 完整的器官交易系统
- 迷药、解剖、求救、医疗功能
- 防滥用机制
- 模块化设计

## 支持

如有问题或建议，请联系开发者。

## 许可证

本插件仅供学习和研究使用，请勿用于商业用途。
