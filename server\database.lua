-- 数据库初始化脚本

local function InitializeDatabase()
    -- 创建器官交易记录表
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `organ_trades` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `victim_identifier` varchar(50) NOT NULL,
            `surgeon_identifier` varchar(50) NOT NULL,
            `organ_type` varchar(50) NOT NULL,
            `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
            `location` varchar(100) DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `victim_identifier` (`victim_identifier`),
            KEY `surgeon_identifier` (`surgeon_identifier`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]])

    -- 创建解剖冷却表
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `surgery_cooldowns` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `victim_identifier` varchar(50) NOT NULL,
            `last_surgery` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `victim_identifier` (`victim_identifier`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]])

    -- 创建求救记录表
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `rescue_calls` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `victim_identifier` varchar(50) NOT NULL,
            `location_x` float NOT NULL,
            `location_y` float NOT NULL,
            `location_z` float NOT NULL,
            `status` enum('active','responded','completed','expired') DEFAULT 'active',
            `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
            `responder_identifier` varchar(50) DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `victim_identifier` (`victim_identifier`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]])

    -- 创建玩家器官状态表
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `player_organs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `identifier` varchar(50) NOT NULL,
            `organ_heart` tinyint(1) DEFAULT 1,
            `organ_liver` tinyint(1) DEFAULT 1,
            `organ_kidney` tinyint(1) DEFAULT 1,
            `organ_lung` tinyint(1) DEFAULT 1,
            `organ_eye` tinyint(1) DEFAULT 1,
            `organ_pancreas` tinyint(1) DEFAULT 1,
            `organ_spleen` tinyint(1) DEFAULT 1,
            `last_updated` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `identifier` (`identifier`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]])

    print('^2[器官交易系统] ^7数据库表初始化完成')
end

-- 检查玩家解剖冷却
function CheckSurgeryCooldown(identifier)
    local result = MySQL.scalar.await('SELECT TIMESTAMPDIFF(SECOND, last_surgery, NOW()) as time_diff FROM surgery_cooldowns WHERE victim_identifier = ?', {identifier})
    
    if result then
        return result >= (Config.Surgery.cooldown / 1000) -- 转换为秒
    end
    
    return true -- 没有记录，可以进行解剖
end

-- 设置解剖冷却
function SetSurgeryCooldown(identifier)
    MySQL.query('INSERT INTO surgery_cooldowns (victim_identifier, last_surgery) VALUES (?, NOW()) ON DUPLICATE KEY UPDATE last_surgery = NOW()', {identifier})
end

-- 记录器官交易
function LogOrganTrade(victimId, surgeonId, organType, location)
    MySQL.insert('INSERT INTO organ_trades (victim_identifier, surgeon_identifier, organ_type, location) VALUES (?, ?, ?, ?)', {
        victimId, surgeonId, organType, location
    })
end

-- 创建求救记录
function CreateRescueCall(identifier, coords)
    return MySQL.insert.await('INSERT INTO rescue_calls (victim_identifier, location_x, location_y, location_z) VALUES (?, ?, ?, ?)', {
        identifier, coords.x, coords.y, coords.z
    })
end

-- 更新求救状态
function UpdateRescueCall(callId, status, responder)
    MySQL.query('UPDATE rescue_calls SET status = ?, responder_identifier = ? WHERE id = ?', {
        status, responder, callId
    })
end

-- 获取玩家器官状态
function GetPlayerOrgans(identifier)
    local result = MySQL.single.await('SELECT * FROM player_organs WHERE identifier = ?', {identifier})
    
    if not result then
        -- 创建默认器官状态
        MySQL.insert('INSERT INTO player_organs (identifier) VALUES (?)', {identifier})
        return {
            organ_heart = 1, organ_liver = 1, organ_kidney = 1, organ_lung = 1,
            organ_eye = 1, organ_pancreas = 1, organ_spleen = 1
        }
    end
    
    return result
end

-- 更新玩家器官状态
function UpdatePlayerOrgan(identifier, organType, status)
    MySQL.query('INSERT INTO player_organs (identifier, ' .. organType .. ') VALUES (?, ?) ON DUPLICATE KEY UPDATE ' .. organType .. ' = ?', {
        identifier, status, status
    })
end

-- 修复玩家器官
function RepairPlayerOrgan(identifier, organType)
    UpdatePlayerOrgan(identifier, organType, 1)
end

-- 导出函数
exports('CheckSurgeryCooldown', CheckSurgeryCooldown)
exports('SetSurgeryCooldown', SetSurgeryCooldown)
exports('LogOrganTrade', LogOrganTrade)
exports('CreateRescueCall', CreateRescueCall)
exports('UpdateRescueCall', UpdateRescueCall)
exports('GetPlayerOrgans', GetPlayerOrgans)
exports('UpdatePlayerOrgan', UpdatePlayerOrgan)
exports('RepairPlayerOrgan', RepairPlayerOrgan)

-- 初始化数据库
CreateThread(function()
    Wait(1000) -- 等待MySQL连接
    InitializeDatabase()
end)
