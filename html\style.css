/* 器官交易系统样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: transparent;
    color: #ffffff;
    overflow: hidden;
}

/* 主面板样式 */
.ui-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(20, 20, 20, 0.95);
    border: 2px solid #ff4444;
    border-radius: 10px;
    box-shadow: 0 0 30px rgba(255, 68, 68, 0.5);
    min-width: 800px;
    min-height: 600px;
    z-index: 1000;
}

.panel-header {
    background: linear-gradient(135deg, #ff4444, #cc0000);
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h2 {
    color: #ffffff;
    font-size: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: #ffffff;
    font-size: 24px;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.panel-content {
    padding: 20px;
    display: flex;
    gap: 30px;
}

/* 人体图样式 */
.human-body {
    position: relative;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.body-image {
    max-width: 300px;
    max-height: 500px;
    opacity: 0.8;
    filter: sepia(100%) hue-rotate(320deg) saturate(2);
}

.organ-buttons {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.organ-btn {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 68, 68, 0.8);
    border: 2px solid #ffffff;
    color: #ffffff;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
}

.organ-btn:hover {
    background: rgba(255, 68, 68, 1);
    transform: scale(1.2);
    box-shadow: 0 0 20px rgba(255, 68, 68, 0.8);
}

.organ-btn.disabled {
    background: rgba(100, 100, 100, 0.5);
    cursor: not-allowed;
    box-shadow: none;
}

.organ-btn.disabled:hover {
    transform: none;
}

/* 器官信息样式 */
.organ-info {
    flex: 1;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 20px;
}

.organ-info h3 {
    color: #ff4444;
    margin-bottom: 15px;
    font-size: 20px;
    text-align: center;
}

.organ-list {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.organ-item {
    background: rgba(255, 255, 255, 0.1);
    margin-bottom: 10px;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #ff4444;
    transition: all 0.3s ease;
}

.organ-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

.organ-item.unavailable {
    opacity: 0.5;
    border-left-color: #666666;
}

.organ-name {
    font-weight: bold;
    color: #ffffff;
    font-size: 16px;
}

.organ-price {
    color: #00ff00;
    font-size: 14px;
    margin-top: 5px;
}

.organ-status {
    float: right;
    font-size: 18px;
}

.organ-status.available {
    color: #00ff00;
}

.organ-status.unavailable {
    color: #ff4444;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.btn-danger {
    background: linear-gradient(135deg, #ff4444, #cc0000);
    color: #ffffff;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ff6666, #ff0000);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 68, 68, 0.4);
}

.action-buttons {
    text-align: center;
    margin-top: 20px;
}

/* 进度条样式 */
.progress-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    padding: 30px;
    border-radius: 10px;
    border: 2px solid #ff4444;
    z-index: 2000;
    min-width: 400px;
}

.progress-content {
    text-align: center;
}

.progress-text {
    color: #ffffff;
    font-size: 18px;
    margin-bottom: 15px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff4444, #ff6666);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-percentage {
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
}

/* 通知样式 */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 3000;
    max-width: 400px;
}

.notification {
    background: rgba(0, 0, 0, 0.9);
    border-left: 4px solid #ff4444;
    padding: 15px 20px;
    margin-bottom: 10px;
    border-radius: 5px;
    color: #ffffff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left-color: #00ff00;
}

.notification.error {
    border-left-color: #ff4444;
}

.notification.info {
    border-left-color: #4444ff;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 倒计时样式 */
.countdown-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 68, 68, 0.9);
    padding: 15px 30px;
    border-radius: 10px;
    z-index: 2500;
    text-align: center;
}

.countdown-text {
    color: #ffffff;
    font-size: 16px;
    margin-bottom: 5px;
}

.countdown-time {
    color: #ffffff;
    font-size: 24px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

/* 状态显示样式 */
.status-container {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #333333;
    z-index: 1500;
}

.status-item {
    margin-bottom: 8px;
    font-size: 14px;
}

.status-label {
    color: #cccccc;
}

.status-value {
    color: #ffffff;
    font-weight: bold;
    margin-left: 5px;
}

/* 器官状态显示 */
.organ-status-container {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #ff4444;
    border-radius: 10px;
    min-width: 300px;
    z-index: 2000;
}

.organ-status-header {
    background: linear-gradient(135deg, #ff4444, #cc0000);
    padding: 10px 15px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.organ-status-header h3 {
    color: #ffffff;
    font-size: 18px;
}

.organ-status-content {
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.organ-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.organ-status-item:last-child {
    border-bottom: none;
}

.organ-status-name {
    color: #ffffff;
    font-size: 14px;
}

.organ-status-indicator {
    font-size: 16px;
}

.organ-status-indicator.healthy {
    color: #00ff00;
}

.organ-status-indicator.damaged {
    color: #ff4444;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 68, 68, 0.6);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 68, 68, 0.8);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .ui-panel {
        min-width: 90vw;
        min-height: 80vh;
    }

    .panel-content {
        flex-direction: column;
    }

    .human-body {
        margin-bottom: 20px;
    }
}

/* 动画效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.organ-btn.selected {
    animation: pulse 1s infinite;
    background: rgba(255, 255, 0, 0.8);
    border-color: #ffff00;
}
