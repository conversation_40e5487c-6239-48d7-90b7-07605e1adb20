-- 解剖系统客户端

local isInSurgery = false
local surgeryBlip = nil

-- 使用手术刀
function UseSurgeryKnife()
    -- 获取附近被迷晕的玩家
    TriggerServerEvent('organ_trade:getNearbyDruggedPlayers')
end

-- 接收附近被迷晕的玩家
RegisterNetEvent('organ_trade:receiveNearbyDruggedPlayers', function(players)
    if #players == 0 then
        ESX.ShowNotification('附近没有被迷晕的玩家', 'error')
        return
    end
    
    -- 创建玩家选择菜单
    local elements = {}
    
    for _, player in ipairs(players) do
        table.insert(elements, {
            label = string.format('%s (距离: %.1fm)', player.name, player.distance),
            value = player.id
        })
    end
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'surgery_target_menu', {
        title = '选择解剖目标',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        local targetId = data.current.value
        menu.close()
        
        -- 确认对话框
        ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'surgery_confirm', {
            title = '确认开始解剖手术？'
        }, function(data2, menu2)
            if data2.value == 'yes' then
                TriggerServerEvent('organ_trade:startSurgery', targetId)
            end
            menu2.close()
        end, function(data2, menu2)
            menu2.close()
        end)
        
    end, function(data, menu)
        menu.close()
    end)
end)

-- 打开器官选择界面
RegisterNetEvent('organ_trade:openOrganSelection', function(targetId)
    -- 先获取目标玩家的器官状态
    TriggerServerEvent('organ_trade:getPlayerOrganStatus', targetId)
    
    -- 等待器官状态响应
    local organStatus = nil
    local handler = nil
    
    handler = RegisterNetEvent('organ_trade:receiveOrganStatus', function(organs)
        organStatus = organs
        RemoveEventHandler(handler)
    end)
    
    -- 等待响应
    CreateThread(function()
        local timeout = 0
        while not organStatus and timeout < 50 do -- 5秒超时
            Wait(100)
            timeout = timeout + 1
        end
        
        if not organStatus then
            ESX.ShowNotification('获取器官状态失败', 'error')
            return
        end
        
        -- 创建器官选择菜单
        ShowOrganSelectionMenu(targetId, organStatus)
    end)
end)

-- 显示器官选择菜单
function ShowOrganSelectionMenu(targetId, organStatus)
    local elements = {}
    
    -- 添加人体平面图说明
    table.insert(elements, {
        label = '=== 人体器官平面图 ===',
        value = nil
    })
    
    for key, organ in pairs(Config.Surgery.organs) do
        local status = '✓'
        local color = '~g~'
        
        if organStatus[organ.item] == 0 then
            status = '✗'
            color = '~r~'
        end
        
        table.insert(elements, {
            label = string.format('%s[%s] %s%s~w~ - $%d', color, key, organ.name, status, organ.price),
            value = organStatus[organ.item] == 1 and key or nil,
            disabled = organStatus[organ.item] == 0
        })
    end
    
    table.insert(elements, {
        label = '取消手术',
        value = 'cancel'
    })
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'organ_selection_menu', {
        title = '选择要提取的器官',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        if data.current.value == 'cancel' then
            TriggerServerEvent('organ_trade:cancelSurgery', targetId)
            menu.close()
            return
        end
        
        if not data.current.value then
            return
        end
        
        local organKey = data.current.value
        local organ = Config.Surgery.organs[organKey]
        
        menu.close()
        
        -- 确认提取
        ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'extract_confirm', {
            title = string.format('确认提取 %s？', organ.name)
        }, function(data2, menu2)
            if data2.value == 'yes' then
                TriggerServerEvent('organ_trade:extractOrgan', targetId, organKey)
            else
                -- 重新打开器官选择菜单
                ShowOrganSelectionMenu(targetId, organStatus)
            end
            menu2.close()
        end, function(data2, menu2)
            menu2.close()
            ShowOrganSelectionMenu(targetId, organStatus)
        end)
        
    end, function(data, menu)
        menu.close()
        TriggerServerEvent('organ_trade:cancelSurgery', targetId)
    end)
end

-- 开始提取动画
RegisterNetEvent('organ_trade:startExtraction', function(duration)
    local playerPed = PlayerPedId()
    
    -- 播放手术动画
    RequestAnimDict('amb@medic@standing@kneel@base')
    while not HasAnimDictLoaded('amb@medic@standing@kneel@base') do
        Wait(100)
    end
    
    TaskPlayAnim(playerPed, 'amb@medic@standing@kneel@base', 'base', 8.0, -8.0, duration, 1, 0, false, false, false)
    
    -- 显示进度条
    exports['progressBars']:startUI(duration, '正在提取器官...')
    
    -- 禁用控制
    CreateThread(function()
        local endTime = GetGameTimer() + duration
        while GetGameTimer() < endTime do
            DisableAllControlActions(0)
            EnableControlAction(0, 1, true)   -- 鼠标移动
            EnableControlAction(0, 2, true)   -- 鼠标移动
            Wait(0)
        end
    end)
end)

-- 检查手术状态
function CheckSurgeryStatus()
    TriggerServerEvent('organ_trade:checkSurgeryStatus')
end

-- 接收手术状态响应
RegisterNetEvent('organ_trade:surgeryStatusResponse', function(inSurgery)
    isInSurgery = inSurgery
end)

-- 创建解剖地点标记
CreateThread(function()
    for _, location in ipairs(Config.Surgery.locations) do
        local blip = AddBlipForCoord(location.x, location.y, location.z)
        SetBlipSprite(blip, 310)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.8)
        SetBlipColour(blip, 1)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString(location.name)
        EndTextCommandSetBlipName(blip)
    end
end)

-- 玩家生成时检查状态
AddEventHandler('playerSpawned', function()
    CreateThread(function()
        Wait(2000)
        CheckSurgeryStatus()
    end)
end)

-- 导出函数
exports('UseSurgeryKnife', UseSurgeryKnife)
exports('IsInSurgery', function() return isInSurgery end)

print('^2[器官交易系统] ^7解剖系统客户端已加载')
